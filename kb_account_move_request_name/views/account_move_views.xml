<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Account Move Form View - Add req_id_name field -->
        <record id="view_move_form_inherit_req_id_name" model="ir.ui.view">
            <field name="name">account.move.form.inherit.req.id.name</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='ref']" position="after">
                    <field name="req_id_name" readonly="1" attrs="{'invisible': [('req_id_name', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <!-- Account Move Tree View - Add req_id_name field as optional -->
        <record id="view_account_move_tree_inherit_req_id_name" model="ir.ui.view">
            <field name="name">account.move.tree.inherit.req.id.name</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="req_id_name" optional="hide"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
