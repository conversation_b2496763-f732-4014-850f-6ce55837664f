<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Server Action to update single account move reference -->
        <record id="action_update_single_account_move_reference" model="ir.actions.server">
            <field name="name">Update Reference with Request Name</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="state">code</field>
            <field name="code">
if records:
    result = records.action_update_reference_with_request_name()
    action = result
            </field>
        </record>

        <!-- Account Move Form View - Add req_id_name field and update button -->
        <record id="view_move_form_inherit_req_id_name" model="ir.ui.view">
            <field name="name">account.move.form.inherit.req.id.name</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='ref']" position="after">
                    <field name="req_id_name" readonly="1" attrs="{'invisible': [('req_id_name', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <!-- Account Move Tree View - Add req_id_name field as optional -->
        <record id="view_account_move_tree_inherit_req_id_name" model="ir.ui.view">
            <field name="name">account.move.tree.inherit.req.id.name</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="req_id_name" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Invoice Tree View - Add req_id_name field as optional -->
        <record id="view_invoice_tree_inherit_req_id_name" model="ir.ui.view">
            <field name="name">account.move.invoice.tree.inherit.req.id.name</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="req_id_name" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Action to update all account moves references -->
        <record id="action_update_all_account_moves_references" model="ir.actions.server">
            <field name="name">Update All References with Request Names</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="state">code</field>
            <field name="code">
result = model.update_all_references_with_request_names()
action = result
            </field>
        </record>



    </data>
</odoo>
