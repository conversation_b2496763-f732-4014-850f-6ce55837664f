{
    'name': 'Account Move Request Name',
    'version': '********.1',
    'summary': 'Override account move field to take name from kb_request_for_sale',
    'description': """
        This module inherits account.move and overrides a field to automatically
        populate with the name from the related kb_request_for_sale record.

        Features:
        - Automatically sets request name from kb_request_for_sale
        - Computed field that updates when request is changed
        - Proper integration with existing sales request workflow
        - Safe handling of optional dependencies
    """,
    'category': 'Accounting',
    'author': 'Knowledge Bonds',
    'website': "https://www.knowledge-bonds.com",
    'depends': [
        'base',
        'account',
        'stock',
        'stock_account',
        'purchase_stock',
        'sale',
        'purchase',
        'kb_request_for_Sale',
    ],
    'external_dependencies': {
        'python': [],
    },
    'data': [
        'security/ir.model.access.csv',
        'views/account_move_views.xml',
        'views/stock_valuation_layer_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
    'sequence': 100,
}
