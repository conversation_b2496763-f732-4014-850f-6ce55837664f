{
    'name': 'Account Move Request Name',
    'version': '********.0',
    'summary': 'Override account move field to take name from kb_request_for_sale',
    'description': """
        This module inherits account.move and overrides a field to automatically
        populate with the name from the related kb_request_for_sale record.
        
        Features:
        - Automatically sets request name from kb_request_for_sale
        - Computed field that updates when request is changed
        - Proper integration with existing sales request workflow
    """,
    'category': 'Accounting',
    'author': 'Knowledge Bonds',
    'website': "https://www.knowledge-bonds.com",
    'depends': [
        'account',
        'stock',
        'stock_account',
        'purchase_stock',
        'kb_request_for_Sale',
        'kb_sales_request_update',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/account_move_views.xml',
        'views/stock_valuation_layer_views.xml',
    ],


    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
    # 'post_init_hook': 'post_init_hook',  # Commented out for faster installation
}
