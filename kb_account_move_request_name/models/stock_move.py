from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class StockMove(models.Model):
    _inherit = 'stock.move'

    def _prepare_account_move_vals(self, credit_account_id, debit_account_id, journal_id, qty, description, svl_id, cost):
        """Override to add req_id when creating account moves from stock valuation"""
        vals = super()._prepare_account_move_vals(credit_account_id, debit_account_id, journal_id, qty, description, svl_id, cost)

        try:
            req_id, request_name = self._get_request_info_from_purchase()
            if req_id:
                vals.update({
                    'req_id': req_id,
                    'stock_move_id': self.id,
                    'ref': self._build_reference_with_request_name(description, request_name)
                })
        except Exception as e:
            _logger.warning(f"Error preparing account move vals for stock move {self.id}: {e}")

        return vals

    def _get_request_info_from_purchase(self):
        """Get request ID and name from purchase order"""
        if not (self.purchase_line_id and
                self.purchase_line_id.order_id and
                hasattr(self.purchase_line_id.order_id, 'req_id') and
                self.purchase_line_id.order_id.req_id):
            return False, False

        req_record = self.purchase_line_id.order_id.req_id
        request_name = getattr(req_record, 'kb_sales_ids', False)
        return req_record.id, request_name

    def _build_reference_with_request_name(self, description, request_name):
        """Build reference string with request name"""
        if not request_name:
            return description or ''

        if description:
            return f"{description} - {request_name}"
        return request_name

    def _account_entry_move(self, qty, description, svl_id, cost):
        """Override to ensure proper req_id propagation in account moves"""
        return super().with_context(stock_valuation=True)._account_entry_move(qty, description, svl_id, cost)
