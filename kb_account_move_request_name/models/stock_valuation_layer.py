from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class StockValuationLayer(models.Model):
    _inherit = 'stock.valuation.layer'

    # ========== FIELDS ==========

    account_move_id = fields.Many2one(
        'account.move',
        'Journal Entry',
        readonly=True,
        check_company=True,
        index="btree_not_null"
    )

    req_id_name = fields.Char(
        string='Request Name',
        compute='_compute_req_id_name',
        store=False,
        help='Name of the sales request from the related account move'
    )

    # ========== UTILITY METHODS ==========

    def _has_kb_request_id_field(self):
        """Check if kb_request_id field exists (from kb_stock_discount module)"""
        return 'kb_request_id' in self._fields

    def _get_kb_request_id_safely(self):
        """Safely get kb_request_id value if field exists"""
        return getattr(self, 'kb_request_id', False) if self._has_kb_request_id_field() else False

    def _get_request_from_purchase_order(self):
        """Get request from purchase order if available"""
        if not (self.stock_move_id and
                self.stock_move_id.purchase_line_id and
                hasattr(self.stock_move_id.purchase_line_id.order_id, 'req_id')):
            return False

        purchase_order = self.stock_move_id.purchase_line_id.order_id
        if purchase_order.req_id and hasattr(purchase_order.req_id, 'kb_sales_ids'):
            return purchase_order.req_id
        return False

    def _get_request_name_from_req_id(self, req_id):
        """Extract request name from req_id object"""
        return req_id.kb_sales_ids if req_id and hasattr(req_id, 'kb_sales_ids') else False

    # ========== COMPUTE METHODS ==========

    @api.depends('account_move_id', 'account_move_id.req_id_name')
    def _compute_req_id_name(self):
        """Compute the request name from various sources"""
        for record in self:
            record.req_id_name = record._get_request_name()

    def _get_request_name(self):
        """Get request name from various sources"""
        try:
            # 1. Try account move first
            if self.account_move_id and self.account_move_id.req_id_name:
                return self.account_move_id.req_id_name

            # 2. Try direct kb_request_id (for discount actions)
            kb_request_id = self._get_kb_request_id_safely()
            request_name = self._get_request_name_from_req_id(kb_request_id)
            if request_name:
                return request_name

            # 3. Try purchase order
            req_id = self._get_request_from_purchase_order()
            return self._get_request_name_from_req_id(req_id)

        except Exception as e:
            _logger.warning(f"Error getting request name for SVL {self.id}: {e}")
            return False

    # ========== CRUD METHODS ==========

    @api.model
    def create(self, vals):
        """Override create to set req_id in account move when SVL is created"""
        record = super().create(vals)
        record._update_account_move_with_request_info()
        return record

    def _update_account_move_with_request_info(self):
        """Update account move with request information"""
        if not self.account_move_id:
            return

        try:
            request_id, request_name = self._get_request_info()
            if request_id:
                self._set_req_id_in_account_move(request_id)
                if request_name:
                    self._update_account_move_reference(request_name)
                    self._trigger_account_move_recomputation()

        except Exception as e:
            _logger.warning(f"Error updating account move for SVL {self.id}: {e}")

    def _get_request_info(self):
        """Get request ID and name from various sources"""
        # Try direct kb_request_id first
        kb_request_id = self._get_kb_request_id_safely()
        if kb_request_id:
            request_name = self._get_request_name_from_req_id(kb_request_id)
            return kb_request_id.id, request_name

        # Try purchase order
        req_id = self._get_request_from_purchase_order()
        if req_id:
            request_name = self._get_request_name_from_req_id(req_id)
            return req_id.id, request_name

        return False, False

    def _set_req_id_in_account_move(self, request_id):
        """Set req_id in account move if field exists"""
        if (hasattr(self.account_move_id, '_has_req_id_field') and
            self.account_move_id._has_req_id_field()):
            self.account_move_id.write({'req_id': request_id})

    def _update_account_move_reference(self, request_name):
        """Update account move reference with request name"""
        current_ref = self.account_move_id.ref or ''
        if request_name not in current_ref:
            new_ref = f"{current_ref} - {request_name}" if current_ref else request_name
            self.account_move_id.write({'ref': new_ref})

    def _trigger_account_move_recomputation(self):
        """Trigger recomputation of account move fields"""
        if hasattr(self.account_move_id, '_compute_req_id_name'):
            self.account_move_id._compute_req_id_name()

    def write(self, vals):
        """Override write to handle account_move_id changes"""
        result = super().write(vals)

        if 'account_move_id' in vals:
            for record in self:
                record._update_account_move_with_request_info()

        return result

    # ========== ACTION METHODS ==========

    def action_update_account_move_req_id(self):
        """Action to update req_id in related account moves"""
        for record in self:
            record._update_account_move_with_request_info()

    @api.model
    def update_all_account_moves_req_id(self):
        """Batch update all stock valuation layers to set req_id in their account moves"""
        try:
            domain = self._build_update_domain()
            svls = self.search(domain)

            updated_count = 0
            for svl in svls:
                if svl.account_move_id and not getattr(svl.account_move_id, 'req_id', False):
                    svl._update_account_move_with_request_info()
                    updated_count += 1

            return self._create_notification_action(
                _('Update Complete'),
                _('Updated %d account moves with request information.') % updated_count,
                'success'
            )
        except Exception as e:
            _logger.error(f"Error in batch update: {e}")
            return self._create_notification_action(
                _('Error'),
                _('An error occurred during batch update.'),
                'danger'
            )

    def _build_update_domain(self):
        """Build domain for finding SVLs that need updating"""
        base_domain = [('account_move_id', '!=', False)]

        if self._has_kb_request_id_field():
            return base_domain + [
                '|',
                ('stock_move_id.purchase_line_id.order_id.req_id', '!=', False),
                ('kb_request_id', '!=', False)
            ]
        else:
            return base_domain + [
                ('stock_move_id.purchase_line_id.order_id.req_id', '!=', False)
            ]

    def _create_notification_action(self, title, message, notification_type):
        """Create a notification action"""
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': title,
                'message': message,
                'type': notification_type,
            }
        }


