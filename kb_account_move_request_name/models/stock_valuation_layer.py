from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class StockValuationLayer(models.Model):
    _inherit = 'stock.valuation.layer'

    def _has_kb_request_id_field(self):
        """Check if kb_request_id field exists (from kb_stock_discount module)"""
        return 'kb_request_id' in self._fields

    def _get_kb_request_id_safely(self):
        """Safely get kb_request_id value if field exists"""
        if self._has_kb_request_id_field():
            return getattr(self, 'kb_request_id', False)
        return False

    # Override account_move_id to automatically set req_id when account move is linked
    account_move_id = fields.Many2one(
        'account.move',
        'Journal Entry',
        readonly=True,
        check_company=True,
        index="btree_not_null"
    )

    # Add computed field to show request name
    req_id_name = fields.Char(
        string='Request Name',
        compute='_compute_req_id_name',
        store=False,
        help='Name of the sales request from the related account move'
    )

    @api.depends('account_move_id', 'account_move_id.req_id_name')
    def _compute_req_id_name(self):
        """Compute the request name from account move, purchase order, or direct kb_request_id"""
        for record in self:
            request_name = False

            try:
                # First try to get from account move
                if record.account_move_id and record.account_move_id.req_id_name:
                    request_name = record.account_move_id.req_id_name

                # Try to get from direct kb_request_id (for discount actions)
                elif record._has_kb_request_id_field():
                    kb_request_id = record._get_kb_request_id_safely()
                    if kb_request_id and hasattr(kb_request_id, 'kb_sales_ids'):
                        request_name = kb_request_id.kb_sales_ids

                # If not available, try to get from purchase order
                elif (record.stock_move_id and
                      record.stock_move_id.purchase_line_id and
                      hasattr(record.stock_move_id.purchase_line_id.order_id, 'req_id')):

                    purchase_order = record.stock_move_id.purchase_line_id.order_id
                    if purchase_order.req_id and hasattr(purchase_order.req_id, 'kb_sales_ids'):
                        request_name = purchase_order.req_id.kb_sales_ids

            except Exception as e:
                _logger.warning(f"Error computing req_id_name for SVL {record.id}: {e}")
                request_name = False

            record.req_id_name = request_name or False

    @api.model
    def create(self, vals):
        """Override create to set req_id in account move when SVL is created"""
        record = super().create(vals)

        request_id = None
        request_name = None

        try:
            # Check if this SVL has a direct kb_request_id (from discount actions)
            if record._has_kb_request_id_field():
                kb_request_id = record._get_kb_request_id_safely()
                if kb_request_id and hasattr(kb_request_id, 'kb_sales_ids'):
                    request_id = kb_request_id.id
                    request_name = kb_request_id.kb_sales_ids

            # If not, check if this SVL has a stock move from a purchase order with req_id
            elif (record.stock_move_id and
                  record.stock_move_id.purchase_line_id and
                  hasattr(record.stock_move_id.purchase_line_id.order_id, 'req_id')):

                purchase_order = record.stock_move_id.purchase_line_id.order_id
                if purchase_order.req_id and hasattr(purchase_order.req_id, 'kb_sales_ids'):
                    request_id = purchase_order.req_id.id
                    request_name = purchase_order.req_id.kb_sales_ids

            # If we have a request and an account move, update the account move
            if request_id and record.account_move_id:
                # Check if account move has req_id field before setting it
                if hasattr(record.account_move_id, '_has_req_id_field') and record.account_move_id._has_req_id_field():
                    record.account_move_id.write({'req_id': request_id})

                # Update reference to include request name
                if request_name:
                    current_ref = record.account_move_id.ref or ''
                    if request_name not in current_ref:
                        if current_ref:
                            new_ref = f"{current_ref} - {request_name}"
                        else:
                            new_ref = request_name
                        record.account_move_id.write({'ref': new_ref})

                # Trigger recomputation of req_id_name
                if hasattr(record.account_move_id, '_compute_req_id_name'):
                    record.account_move_id._compute_req_id_name()

        except Exception as e:
            _logger.warning(f"Error setting req_id in account move during SVL creation: {e}")

        return record

    def write(self, vals):
        """Override write to handle account_move_id changes"""
        result = super().write(vals)

        # If account_move_id is being set or changed
        if 'account_move_id' in vals:
            for record in self:
                if record.account_move_id:
                    request_id = None
                    request_name = None

                    # Check if this SVL has a direct kb_request_id (from discount actions)
                    if hasattr(record, 'kb_request_id') and getattr(record, 'kb_request_id', False):
                        request_id = record.kb_request_id.id
                        request_name = record.kb_request_id.kb_sales_ids

                    # If not, check if this SVL has a stock move from a purchase order with req_id
                    elif record.stock_move_id and record.stock_move_id.purchase_line_id:
                        purchase_order = record.stock_move_id.purchase_line_id.order_id
                        if purchase_order.req_id:
                            request_id = purchase_order.req_id.id
                            request_name = purchase_order.req_id.kb_sales_ids

                    # If we have a request, update the account move
                    if request_id:
                        # Set req_id in the account move
                        record.account_move_id.write({
                            'req_id': request_id
                        })

                        # Update reference to include request name
                        if request_name:
                            current_ref = record.account_move_id.ref or ''
                            if request_name not in current_ref:
                                if current_ref:
                                    new_ref = f"{current_ref} - {request_name}"
                                else:
                                    new_ref = request_name
                                record.account_move_id.write({'ref': new_ref})

                        # Trigger recomputation of req_id_name
                        record.account_move_id._compute_req_id_name()

        return result

    def _validate_accounting_entries(self):
        """Override to ensure account moves created from stock valuation get proper req_id"""
        # Get the request information before creating account moves
        request_info = {}
        for svl in self:
            # Check if this SVL has a direct kb_request_id (from discount actions)
            if hasattr(svl, 'kb_request_id') and getattr(svl, 'kb_request_id', False):
                request_info[svl.id] = svl.kb_request_id.id

            # If not, check if this SVL has a stock move from a purchase order with req_id
            elif svl.stock_move_id and svl.stock_move_id.purchase_line_id:
                purchase_order = svl.stock_move_id.purchase_line_id.order_id
                if purchase_order.req_id:
                    request_info[svl.id] = purchase_order.req_id.id

        # Call the original method to create account moves
        result = super()._validate_accounting_entries()

        # Update the created account moves with req_id and request name in reference
        if request_info:
            for svl in self:
                if svl.id in request_info and svl.account_move_id:
                    request_record = self.env['kb_request_for_sale'].browse(request_info[svl.id])
                    request_name = request_record.kb_sales_ids

                    # Update account move with req_id
                    svl.account_move_id.write({
                        'req_id': request_info[svl.id]
                    })

                    # Update reference to include request name
                    current_ref = svl.account_move_id.ref or ''
                    if request_name and request_name not in current_ref:
                        if current_ref:
                            new_ref = f"{current_ref} - {request_name}"
                        else:
                            new_ref = request_name
                        svl.account_move_id.write({'ref': new_ref})

                    # Trigger recomputation of req_id_name field
                    svl.account_move_id._compute_req_id_name()

        return result

    def action_update_account_move_req_id(self):
        """Action to update req_id in related account moves"""
        for record in self:
            if record.account_move_id:
                request_id = None
                request_name = None

                # Check if this SVL has a direct kb_request_id (from discount actions)
                if hasattr(record, 'kb_request_id') and getattr(record, 'kb_request_id', False):
                    request_id = record.kb_request_id.id
                    request_name = record.kb_request_id.kb_sales_ids

                # If not, check if this SVL has a stock move from a purchase order with req_id
                elif record.stock_move_id and record.stock_move_id.purchase_line_id:
                    purchase_order = record.stock_move_id.purchase_line_id.order_id
                    if purchase_order.req_id:
                        request_id = purchase_order.req_id.id
                        request_name = purchase_order.req_id.kb_sales_ids

                # If we have a request, update the account move
                if request_id:
                    # Set req_id in the account move
                    record.account_move_id.write({
                        'req_id': request_id
                    })

                    # Update reference to include request name
                    if request_name:
                        current_ref = record.account_move_id.ref or ''
                        if request_name not in current_ref:
                            if current_ref:
                                new_ref = f"{current_ref} - {request_name}"
                            else:
                                new_ref = request_name
                            record.account_move_id.write({'ref': new_ref})

                    # Trigger recomputation of req_id_name
                    record.account_move_id._compute_req_id_name()

    @api.model
    def update_all_account_moves_req_id(self):
        """Batch update all stock valuation layers to set req_id in their account moves"""
        # Find SVLs that have account moves but the account move doesn't have req_id set
        domain = [('account_move_id', '!=', False)]

        # Add kb_request_id condition only if the field exists
        if 'kb_request_id' in self._fields:
            domain = [
                ('account_move_id', '!=', False),
                '|',
                ('stock_move_id.purchase_line_id.order_id.req_id', '!=', False),
                ('kb_request_id', '!=', False)
            ]
        else:
            domain = [
                ('account_move_id', '!=', False),
                ('stock_move_id.purchase_line_id.order_id.req_id', '!=', False)
            ]

        svls = self.search(domain)

        updated_count = 0
        for svl in svls:
            if not svl.account_move_id.req_id:
                svl.action_update_account_move_req_id()
                updated_count += 1

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Update Complete'),
                'message': _('Updated %d account moves with request information.') % updated_count,
                'type': 'success',
            }
        }


