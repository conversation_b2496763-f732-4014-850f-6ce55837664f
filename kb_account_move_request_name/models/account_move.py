from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class AccountMove(models.Model):
    _inherit = "account.move"

    req_id_name = fields.Char(
        string='Request Name',
        help='Name of the sales request',
        compute='_compute_req_id_name',
        store=True,
        readonly=True
    )

    # ========== UTILITY METHODS ==========

    def _has_req_id_field(self):
        """Check if req_id field exists in account.move model"""
        return 'req_id' in self._fields

    def _get_req_id_safely(self):
        """Safely get req_id value if field exists"""
        return getattr(self, 'req_id', False) if self._has_req_id_field() else False

    def _set_req_id_safely(self, req_id_value):
        """Safely set req_id value if field exists"""
        if self._has_req_id_field():
            self.req_id = req_id_value

    def _get_request_name_from_req_id(self, req_id):
        """Extract request name from req_id object"""
        return req_id.kb_sales_ids if req_id and hasattr(req_id, 'kb_sales_ids') else False

    def _find_sale_order_by_origin(self, origin):
        """Find sale order by origin name"""
        return self.env['sale.order'].search([('name', '=', origin)], limit=1)

    def _get_request_from_sale_order(self, sale_order):
        """Get request from sale order if available"""
        if (sale_order and hasattr(sale_order, 'req_id') and
            sale_order.req_id and hasattr(sale_order.req_id, 'kb_sales_ids')):
            return sale_order.req_id
        return False

    def _get_request_from_purchase_order(self, purchase_order):
        """Get request from purchase order if available"""
        if (purchase_order and hasattr(purchase_order, 'req_id') and
            purchase_order.req_id and hasattr(purchase_order.req_id, 'kb_sales_ids')):
            return purchase_order.req_id
        return False

    def _update_reference_with_request_name(self, request_name):
        """Update journal entry reference with request name"""
        if not request_name:
            return False

        current_ref = self.ref or ''
        if request_name not in current_ref:
            new_ref = f"{current_ref} - {request_name}" if current_ref else request_name
            self.write({'ref': new_ref})
            return True
        return False


    # ========== COMPUTE METHODS ==========

    @api.depends('invoice_origin')
    def _compute_req_id_name(self):
        """Compute req_id_name to store the request name"""
        for move in self:
            move.req_id_name = move._get_request_name()

    def _get_request_name(self):
        """Get request name from various sources"""
        try:
            # 1. Try existing req_id relationship
            req_id = self._get_req_id_safely()
            request_name = self._get_request_name_from_req_id(req_id)
            if request_name:
                return request_name

            # 2. Try stock move -> purchase order
            request_name = self._get_request_name_from_stock_move()
            if request_name:
                return request_name

            # 3. Try stock valuation layers
            request_name = self._get_request_name_from_valuation_layers()
            if request_name:
                return request_name

            # 4. Try invoice origin (sale orders)
            request_name = self._get_request_name_from_invoice_origin()
            if request_name:
                return request_name

        except Exception as e:
            _logger.warning(f"Error getting request name for account move {self.id}: {e}")

        return False

    def _get_request_name_from_stock_move(self):
        """Get request name from stock move purchase order"""
        if not (hasattr(self, 'stock_move_id') and self.stock_move_id):
            return False

        stock_move = self.stock_move_id
        if not (stock_move.purchase_line_id and stock_move.purchase_line_id.order_id):
            return False

        req_id = self._get_request_from_purchase_order(stock_move.purchase_line_id.order_id)
        if req_id:
            self._set_req_id_safely(req_id.id)
            return self._get_request_name_from_req_id(req_id)
        return False

    def _get_request_name_from_valuation_layers(self):
        """Get request name from stock valuation layers"""
        if not hasattr(self, 'line_ids'):
            return False

        for line in self.line_ids:
            if not hasattr(line, 'stock_valuation_layer_ids'):
                continue

            for svl in line.stock_valuation_layer_ids:
                if not (svl.stock_move_id and svl.stock_move_id.purchase_line_id):
                    continue

                req_id = self._get_request_from_purchase_order(svl.stock_move_id.purchase_line_id.order_id)
                if req_id:
                    self._set_req_id_safely(req_id.id)
                    return self._get_request_name_from_req_id(req_id)
        return False

    def _get_request_name_from_invoice_origin(self):
        """Get request name from invoice origin (sale orders)"""
        if not self.invoice_origin:
            return False

        # Handle single origin
        req_id = self._get_request_from_sale_order(self._find_sale_order_by_origin(self.invoice_origin))
        if req_id:
            self._set_req_id_safely(req_id.id)
            return self._get_request_name_from_req_id(req_id)

        # Handle comma-separated origins
        if ',' in self.invoice_origin:
            origins = [origin.strip() for origin in self.invoice_origin.split(',')]
            for origin in origins:
                req_id = self._get_request_from_sale_order(self._find_sale_order_by_origin(origin))
                if req_id:
                    self._set_req_id_safely(req_id.id)
                    return self._get_request_name_from_req_id(req_id)

        return False

    # ========== CRUD METHODS ==========

    @api.model
    def create(self, vals):
        """Override create to populate req_id from stock valuation context"""
        request_name = self._prepare_req_id_for_creation(vals)
        move = super().create(vals)

        if request_name:
            move._update_reference_with_request_name(request_name)

        return move

    def _prepare_req_id_for_creation(self, vals):
        """Prepare req_id and return request name for creation"""
        try:
            if not (self.env.context.get('stock_valuation') or vals.get('stock_move_id')):
                return False

            stock_move_id = vals.get('stock_move_id')
            if not stock_move_id:
                return False

            stock_move = self.env['stock.move'].browse(stock_move_id)
            req_id = self._get_request_from_purchase_order(
                stock_move.purchase_line_id.order_id if stock_move.purchase_line_id else None
            )

            if req_id:
                if self._has_req_id_field():
                    vals['req_id'] = req_id.id
                return self._get_request_name_from_req_id(req_id)

        except Exception as e:
            _logger.warning(f"Error preparing req_id during creation: {e}")

        return False

    def write(self, vals):
        """Override write to update reference when req_id changes"""
        result = super().write(vals)

        if 'req_id' in vals and self._has_req_id_field():
            self._update_references_after_req_id_change()

        return result

    def _update_references_after_req_id_change(self):
        """Update references after req_id field changes"""
        try:
            for move in self:
                req_id = move._get_req_id_safely()
                request_name = move._get_request_name_from_req_id(req_id)

                if request_name:
                    move.sudo()._update_reference_with_request_name(request_name)

        except Exception as e:
            _logger.warning(f"Error updating references after req_id change: {e}")

    # ========== ACTION METHODS ==========

    def action_update_reference_with_request_name(self):
        """Action to update journal entry reference with request name"""
        updated_count = 0

        for move in self:
            try:
                request_name = move._get_request_name_for_update()
                if request_name and move._update_reference_with_request_name(request_name):
                    updated_count += 1
            except Exception as e:
                _logger.warning(f"Error updating reference for move {move.id}: {e}")

        return self._create_notification_action(
            _('Update Complete'),
            _('Updated %d journal entries with request names.') % updated_count,
            'success'
        )

    def _get_request_name_for_update(self):
        """Get request name for manual update action"""
        # Try req_id field first
        req_id = self._get_req_id_safely()
        request_name = self._get_request_name_from_req_id(req_id)

        # Fallback to computed field
        return request_name or self.req_id_name

    @api.model
    def update_all_references_with_request_names(self):
        """Batch update all account moves to include request names in references"""
        if not self._has_req_id_field():
            return self._create_notification_action(
                _('Field Not Available'),
                _('req_id field is not available in account.move model.'),
                'warning'
            )

        try:
            moves = self.search([
                ('req_id', '!=', False),
                ('req_id.kb_sales_ids', '!=', False)
            ])

            updated_count = 0
            for move in moves:
                try:
                    req_id = move._get_req_id_safely()
                    request_name = move._get_request_name_from_req_id(req_id)

                    if request_name and move._update_reference_with_request_name(request_name):
                        updated_count += 1

                except Exception as e:
                    _logger.warning(f"Error updating reference for move {move.id}: {e}")

        except Exception as e:
            _logger.error(f"Error in batch update: {e}")
            updated_count = 0

        return self._create_notification_action(
            _('Update Complete'),
            _('Updated %d journal entries with request names.') % updated_count,
            'success'
        )

    def _create_notification_action(self, title, message, notification_type):
        """Create a notification action"""
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': title,
                'message': message,
                'type': notification_type,
            }
        }

