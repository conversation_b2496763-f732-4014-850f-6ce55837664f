from odoo import api, fields, models, _


class AccountMove(models.Model):
    _inherit = "account.move"

    req_id_name = fields.Char(
        string='Request Name',
        compute='_compute_req_id_name',
        store=True,
        readonly=True
    )

    @api.depends('invoice_origin')
    def _compute_req_id_name(self):
        """Compute req_id_name from sale order"""
        for move in self:
            request_name = False

            if move.invoice_origin:
                # Try to find sale order by invoice origin
                sale_order = self.env['sale.order'].search([
                    ('name', '=', move.invoice_origin)
                ], limit=1)

                if sale_order and hasattr(sale_order, 'req_id') and sale_order.req_id:
                    if hasattr(sale_order.req_id, 'kb_sales_ids'):
                        request_name = sale_order.req_id.kb_sales_ids

            move.req_id_name = request_name

