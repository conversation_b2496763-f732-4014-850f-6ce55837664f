#!/usr/bin/env python3
"""
Test script to check if kb_account_move_request_name module dependencies are available
"""

import sys
import os

# Add Odoo to path
sys.path.insert(0, '/home/<USER>/odoo_master/odoo16')
sys.path.insert(0, '/home/<USER>/odoo_cutom_addons/elmotawea/addons')

def check_module_exists(module_name, search_paths):
    """Check if a module exists in any of the search paths"""
    for path in search_paths:
        module_path = os.path.join(path, module_name)
        if os.path.exists(module_path):
            manifest_path = os.path.join(module_path, '__manifest__.py')
            openerp_path = os.path.join(module_path, '__openerp__.py')
            if os.path.exists(manifest_path) or os.path.exists(openerp_path):
                return True, path
    return False, None

def main():
    # Define search paths for Odoo modules
    search_paths = [
        '/home/<USER>/odoo_master/odoo16/addons',
        '/home/<USER>/odoo_cutom_addons/elmotawea/addons',
        '/home/<USER>/odoo_master/odoo16/odoo/addons',
    ]
    
    # Dependencies from the manifest
    dependencies = [
        'base',
        'account',
        'stock',
        'stock_account', 
        'purchase_stock',
        'sale',
        'purchase',
        'kb_request_for_Sale',
    ]
    
    print("Checking module dependencies...")
    print("=" * 50)
    
    missing_deps = []
    
    for dep in dependencies:
        exists, path = check_module_exists(dep, search_paths)
        if exists:
            print(f"✓ {dep} - Found in {path}")
        else:
            print(f"✗ {dep} - NOT FOUND")
            missing_deps.append(dep)
    
    print("=" * 50)
    
    if missing_deps:
        print(f"Missing dependencies: {missing_deps}")
        print("The module may not install properly due to missing dependencies.")
        return 1
    else:
        print("All dependencies are available!")
        print("The module should be able to install.")
        return 0

if __name__ == "__main__":
    sys.exit(main())
